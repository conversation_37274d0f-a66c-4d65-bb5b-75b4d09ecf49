import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import type { Subject } from '@mrh/shared'
import { PrismaService } from '../prisma/prisma.service'

type JWTPayload = {
  sub: string
  iat?: number
  exp?: number
  iss?: string
  aud?: string
}

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private readonly jwt: JwtService,
    private readonly prisma: PrismaService
  ) {}

  async canActivate(ctx: ExecutionContext): Promise<boolean> {
    const req = ctx.switchToHttp().getRequest()
    const token = this.extractBearer(req.headers['authorization'])
    if (!token) throw new UnauthorizedException('Missing bearer token')

    const payload = await this.verify(token)
    const userId = payload.sub
    if (!userId) throw new UnauthorizedException('Invalid token payload')

    // Load org-scoped memberships for IAM Subject
    const memberships = await this.prisma.membership.findMany({
      where: { userId },
      select: { orgId: true, role: true }
    })

    const subject: Subject = { id: userId, memberships }
    req.user = subject // consumed by PolicyGuard
    return true
  }

  private extractBearer(auth?: string) {
    if (!auth) return null
    const [type, value] = auth.split(' ')
    return type?.toLowerCase() === 'bearer' && value ? value : null
  }

  private async verify(token: string): Promise<JWTPayload> {
    try {
      return await this.jwt.verifyAsync<JWTPayload>(token, {
        secret: process.env.JWT_SECRET!,
        issuer: process.env.JWT_ISSUER,
        audience: process.env.JWT_AUDIENCE
      })
    } catch {
      throw new UnauthorizedException('Invalid or expired token')
    }
  }
}
