import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common'
import { authorize, Subject } from '@mrh/shared'

@Injectable()
export class PolicyGuard implements CanActivate {
	canActivate(ctx: ExecutionContext): boolean {
		const req = ctx.switchToHttp().getRequest()
		const user: Subject = req.user // set by your JWT auth guard
		const { orgId, ownerId } = req.body ?? {}
		authorize(user, 'listing:create', { kind: 'listing', orgId, ownerId })
		return true
	}
}
