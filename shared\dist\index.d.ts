import { z } from 'zod'

type Role = 'ADMIN' | 'BROKER' | 'AGENT' | 'BUYER'
type Action = 'listing:create' | 'listing:update' | 'listing:read'
type Membership = {
	orgId: string
	role: Role
}
type Subject = {
	id: string
	memberships: Membership[]
}
type ListingRes = {
	kind: 'listing'
	orgId: string
	ownerId?: string
}
declare function roleInOrg(user: Subject, orgId: string): Role | undefined
declare function can(user: Subject, action: Action, res: ListingRes): boolean
declare function authorize(user: Subject, action: Action, res: ListingRes): void

declare const CreateListingInput: z.ZodObject<
	{
		orgId: z.ZodString
		title: z.ZodString
		price: z.ZodNumber
	},
	z.core.$strip
>
type CreateListingInput = z.infer<typeof CreateListingInput>
declare function makeCreateListing(deps: {
	prisma: unknown
}): (user: Subject, input: CreateListingInput) => Promise<unknown>

export {
	type Action,
	CreateListingInput,
	type ListingRes,
	type Membership,
	type Role,
	type Subject,
	authorize,
	can,
	makeCreateListing,
	roleInOrg
}
