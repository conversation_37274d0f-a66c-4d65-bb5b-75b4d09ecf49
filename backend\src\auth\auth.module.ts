import { Module } from '@nestjs/common'
import { JwtModule } from '@nestjs/jwt'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { JwtAuthGuard } from './jwt-auth.guard'
import { PolicyGuard } from './policy.guard'
import { PrismaModule } from '../prisma/prisma.module'

@Module({
  imports: [
    PrismaModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1h'),
          issuer: configService.get<string>('JWT_ISSUER'),
          audience: configService.get<string>('JWT_AUDIENCE')
        }
      }),
      inject: [ConfigService]
    })
  ],
  providers: [JwtAuthGuard, PolicyGuard],
  exports: [JwtAuthGuard, PolicyGuard, JwtModule]
})
export class AuthModule {}
