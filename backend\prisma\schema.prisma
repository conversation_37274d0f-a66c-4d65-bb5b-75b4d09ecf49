// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  engineType = "library"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id          String       @id @default(cuid())
  email       String       @unique
  name        String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  memberships Membership[]
  listings    Listing[]

  @@map("users")
}

model Organization {
  id          String       @id @default(cuid())
  name        String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  memberships Membership[]
  listings    Listing[]

  @@map("organizations")
}

model Membership {
  id     String @id @default(cuid())
  userId String
  orgId  String
  role   Role

  user User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  org  Organization @relation(fields: [orgId], references: [id], onDelete: Cascade)

  @@unique([userId, orgId])
  @@map("memberships")
}

model Listing {
  id        String   @id @default(cuid())
  title     String
  price     Int
  ownerId   String
  orgId     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  owner User         @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  org   Organization @relation(fields: [orgId], references: [id], onDelete: Cascade)

  @@map("listings")
}

enum Role {
  ADMIN
  BROKER
  AGENT
  BUYER
}
